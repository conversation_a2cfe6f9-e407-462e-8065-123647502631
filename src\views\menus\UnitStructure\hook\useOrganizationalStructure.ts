import { useEffect, useCallback } from 'react'
import { useOrganizationalStructureStore } from '@/views/menus/UnitStructure/store/organizationalStructureStore'
import { 
    getOrganizeStructureByCode, 
    updateOrganizeStructure, 
    deleteOrganizeStructure
} from '@/services/OrganizeStructureService'
import type {  CreatedOrganizationalStructure } from '@/@types/organizationalStructure'

export const useOrganizationalStructure = (autoFetch: boolean = true) => {
    const store = useOrganizationalStructureStore()

    // Auto-fetch on mount
    useEffect(() => {
        if (autoFetch && !store.organizationalStructure && !store.isLoading) {
            store.fetchOrganizationalStructure()
        }
    }, [autoFetch, store.organizationalStructure, store.isLoading, store.fetchOrganizationalStructure])

    // Fetch by specific code
    const fetchByCode = useCallback(async (code: string) => {
        try {
            const data = await getOrganizeStructureByCode(code)
            return data
        } catch (error) {
            console.error('Error fetching organizational structure by code:', error)
            throw error
        }
    }, [])

    // Create new organizational node
    const createNode = useCallback(async (nodeData: CreatedOrganizationalStructure) => {
        try {
            await store.createNode(nodeData)
        } catch (error) {
            console.error('Error creating organizational node:', error)
            throw error
        }
    }, [store])

    // Update existing organizational node
    const updateNode = useCallback(async (code: string, nodeData: CreatedOrganizationalStructure) => {
        try {
            const data = await updateOrganizeStructure(code, nodeData)
            // Refresh the main structure after update
            await store.fetchOrganizationalStructure()
            return data
        } catch (error) {
            console.error('Error updating organizational node:', error)
            throw error
        }
    }, [store])

    // Delete organizational node
    const deleteNode = useCallback(async (code: string) => {
        try {
            const data = await deleteOrganizeStructure(code)
            // Refresh the main structure after deletion
            await store.fetchOrganizationalStructure()
            return data
        } catch (error) {
            console.error('Error deleting organizational node:', error)
            throw error
        }
    }, [store])

    // Refresh data
    const refresh = useCallback(async () => {
        await store.fetchOrganizationalStructure()
    }, [store])

    return {
        // Store state
        ...store,
        
        // Additional actions
        fetchByCode,
        createNode,
        updateNode,
        deleteNode,
        refresh,
        
        // Convenience methods
        isLoading: store.isLoading,
        error: store.error,
        data: store.organizationalStructure,
        
        // Enhanced getters with better error handling
        getNodeByCode: (code: string) => {
            try {
                return store.getNodeByCode(code)
            } catch (error) {
                console.error('Error getting node by code:', error)
                return undefined
            }
        },
        
        getNodesByLevel: (level: number) => {
            try {
                return store.getNodesByLevel(level)
            } catch (error) {
                console.error('Error getting nodes by level:', error)
                return []
            }
        },
        
        getTotalNodes: () => {
            try {
                return store.getTotalNodes()
            } catch (error) {
                console.error('Error getting total nodes:', error)
                return 0
            }
        },
        
        getLevelCount: (level: 'L1' | 'L2' | 'L3' | 'L4') => {
            try {
                return store.getLevelCount(level)
            } catch (error) {
                console.error('Error getting level count:', error)
                return 0
            }
        }
    }
}
