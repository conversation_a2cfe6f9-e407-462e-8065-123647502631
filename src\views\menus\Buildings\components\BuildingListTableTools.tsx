import BuildingListSearch from './BuildingListSearch'
import BuildingTableFilter from './BuildingTableFilter'
import useBuildingList from '../hooks/useBuildingList'
import cloneDeep from 'lodash/cloneDeep'

const BuildingListTableTools = () => {
    const { tableData, setTableData } = useBuildingList()

    const handleInputChange = (val: string) => {
        const newTableData = cloneDeep(tableData)
        newTableData.query = val
        newTableData.pageIndex = 1
        if (typeof val === 'string' && val.length > 1) {
            setTableData(newTableData)
        }

        if (typeof val === 'string' && val.length === 0) {
            setTableData(newTableData)
        }
    }

    return (
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
            <BuildingListSearch onInputChange={handleInputChange} />
            <BuildingTableFilter />
        </div>
    )
}

export default BuildingListTableTools
