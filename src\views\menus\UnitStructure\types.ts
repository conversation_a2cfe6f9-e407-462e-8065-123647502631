export type Ticket = {
    id: string // maps to code
    name: string
    description?: string
    level?: number
    parentId: string | null // maps to parentCode
    childrenCount?: number
    createdAt?: string
    assignedBy?: string
    label?: string
}

export type TreeNode = Ticket & { children?: TreeNode[] }

export function buildTreeFromFlat(tickets: Ticket[]): TreeNode[] {
    const map = new Map<string, TreeNode>()
    const roots: TreeNode[] = []

    tickets.forEach((ticket) => {
        map.set(ticket.id, { ...ticket, children: [] })
    })

    map.forEach((node) => {
        if (node.parentId && map.has(node.parentId)) {
            map.get(node.parentId)!.children!.push(node)
        } else {
            roots.push(node)
        }
    })

    return roots
}

import type { OrganizationalNode } from '@/@types/organizationalStructure'

// Helper function to convert OrganizationalNode to Ticket
export function convertOrganizationalNodeToTicket(node: OrganizationalNode, parentId: string | null = null): Ticket {
    return {
        id: node.code,
        name: node.name,
        description: node.description || '',
        level: node.level || 1,
        parentId,
        childrenCount: node.childrenCount,
        createdAt: new Date().toISOString(),
        assignedBy: '',
        label: ''
    }
}

// Helper function to flatten organizational structure to tickets
export function flattenOrganizationalStructure(nodes: OrganizationalNode[], parentId: string | null = null): Ticket[] {
    const result: Ticket[] = []
    
    for (const node of nodes || []) {
        result.push(convertOrganizationalNodeToTicket(node, parentId))
        
        if (node.children && node.children.length > 0) {
            result.push(...flattenOrganizationalStructure(node.children, node.code))
        }
    }
    
    return result
}
