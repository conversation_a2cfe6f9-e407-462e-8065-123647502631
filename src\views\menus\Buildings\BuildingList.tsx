import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import BuildingListActionTools from './components/BuildingListActionTools'
import BuildingListTableTools from './components/BuildingListTableTools'
import BuildingListTable from './components/BuildingListTable'
import BuildingListSelected from './components/BuildingListSelected'
import useTranslation from '@/utils/hooks/useTranslation'

const BuildingList = () => {
    const { t } = useTranslation()
    
    return (
        <>
            <Container>
                <AdaptiveCard>
                    <div className="flex flex-col gap-4">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                            <h3>{t('nav.buildings.buildings')}</h3>
                            <BuildingListActionTools />
                        </div>
                        <BuildingListTableTools />
                        <BuildingListTable />
                    </div>
                </AdaptiveCard>
            </Container>
            <BuildingListSelected />
        </>
    )
}

export default BuildingList
