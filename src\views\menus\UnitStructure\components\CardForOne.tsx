import { TreeNode } from '../types'
import {  <PERSON><PERSON><PERSON><PERSON>, Tb<PERSON><PERSON><PERSON>, Tb<PERSON>inus, Tb<PERSON><PERSON>} from 'react-icons/tb'

function CardForOne({ node, hasChildren, isCollapsed, toggleCollapse, onNodeClick }: { 
    node: TreeNode, 
    hasChildren: boolean, 
    isCollapsed: boolean, 
    toggleCollapse: (id: string) => void,
    onNodeClick?: (node: TreeNode) => void
}) {
  return (
    <div className="flex items-center justify-between">
        {/* Parent Header */}
                            <div className="flex items-center gap-2">
                                {hasChildren ? (
                                <button
                                    type="button"
                                    aria-label={isCollapsed ? "Expand" : "Collapse"}
                                    className="shrink-0 p-1 rounded bg-blue-500 hover:bg-blue-600 text-white shadow-sm transition-all duration-200 transform hover:scale-105"
                                    onClick={() => toggleCollapse(node.id)}
                                >
                                    {isCollapsed ? <TbPlus size={12} /> : <Tb<PERSON><PERSON> size={12} />}
                                </button>
                                ) : (
                                    <div className="w-6 h-6 flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded">
                                        <TbFolder className="text-gray-500 dark:text-gray-400" size={12} />
                                    </div>
                                )}
                                <div className="flex items-center gap-1">
                                    <span className="text-sm font-medium text-gray-800 dark:text-gray-100">
                                        {node.name}
                                    </span>
                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                        ({node.id})
                                    </span>
                                </div>
                            </div>
                        {/* Description */}
                        {node.description && (
                            <div className="opacity-0 hover:opacity-100 transition-opacity duration-200 ">
                                <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
                                    {node.description}
                                </p>
                            </div>
                        )}
                        {/* Level and children count */}
                            <div className="flex items-center gap-1">
                                <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-1.5 py-0.5 rounded">
                                    L{node.level}
                                </span>
                                {hasChildren && (
                                    <span className="text-xs bg-blue-500 text-white px-1.5 py-0.5 rounded font-medium shadow-sm">
                                        {node.childrenCount}
                                    </span>
                                )}
                                <span>
                                    <button className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-1.5 py-0.5 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                    onClick={() => onNodeClick?.(node)}>
                                    <TbClick size={12} />
                                </button>
                                </span>
                            </div>

                        </div>
  )
}

export default CardForOne
