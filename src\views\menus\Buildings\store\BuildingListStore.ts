import { create } from 'zustand'
import type { TableQueries } from '@/@types/common'
import type { Buildings, Filter } from '../types'

export const initialTableData: TableQueries = {
    pageIndex: 1,
    pageSize: 10,
    query: '',
    sort: {
        order: '',
        key: '',
    },
}

export const initialFilterData = {
    buildingStatus: '',
    visibleColumns: [
        'name',
        'city',
        'governorate',
        'address',
        'status',
        'assignedBy',
        'action',
    ],
}

export type BuildingListState = {
    tableData: TableQueries
    filterData: Filter
    selectedBuilding: Partial<Buildings>[]
}

type BuildingListAction = {
    setFilterData: (payload: Filter) => void
    setTableData: (payload: TableQueries) => void
    setSelectedBuilding: (checked: boolean, building: Buildings) => void
    setSelectAllBuilding: (building: Buildings[]) => void
}

const initialState: BuildingListState = {
    tableData: initialTableData,
    filterData: initialFilterData,
    selectedBuilding: [],
}

export const useBuildingListStore = create<
    BuildingListState & BuildingListAction
>((set) => ({
    ...initialState,
    setFilterData: (payload) => set(() => ({ filterData: payload })),
    setTableData: (payload) => set(() => ({ tableData: payload })),
    setSelectedBuilding: (checked, row) =>
        set((state) => {
            const prevData = state.selectedBuilding
            if (checked) {
                return { selectedBuilding: [...prevData, ...[row]] }
            } else {
                if (
                    prevData.some(
                        (prevConsumable) => row.id === prevConsumable.id,
                    )
                ) {
                    return {
                        selectedConsumable: prevData.filter(
                            (prevConsumable) => prevConsumable.id !== row.id,
                        ),
                    }
                }
                return { selectedConsumable: prevData }
            }
        }),
    setSelectAllBuilding: (row) => set(() => ({ selectedBuilding: row })),
}))
