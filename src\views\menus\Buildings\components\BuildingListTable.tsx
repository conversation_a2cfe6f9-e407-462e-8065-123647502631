import { useMemo, useState } from 'react'
import Tooltip from '@/components/ui/Tooltip'
import DataTable from '@/components/shared/DataTable'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import useBuildingList from '../hooks/useBuildingList'
import cloneDeep from 'lodash/cloneDeep'
import { TbPencil, TbTrash } from 'react-icons/tb'
import type { OnSortParam, ColumnDef, Row } from '@/components/shared/DataTable'
import type { Buildings } from '../types'
import type { TableQueries } from '@/@types/common'
import { Tag } from '@/components/ui'
import BuildingModal from './BuildingModal'
import useTranslation from '@/utils/hooks/useTranslation'

const ActionColumn = ({
    onEdit,
    onDelete,
}: {
    onEdit: () => void
    onDelete: () => void
}) => {
    const { t } = useTranslation()
    return (
        <div className="flex items-center justify-center gap-3">
            <Tooltip title={t('nav.shared.edit')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={onEdit}
                >
                    <TbPencil />
                </div>
            </Tooltip>
            <Tooltip title={t('nav.shared.delete')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={onDelete}
                >
                    <TbTrash />
                </div>
            </Tooltip>
        </div>
    )
}

const statusColor: Record<string, string> = {
    معتمد: 'bg-emerald-200 dark:bg-emerald-200 text-gray-900 dark:text-gray-900',
    'غير معتمد': 'bg-red-200 dark:bg-red-200 text-gray-900 dark:text-gray-900',
}

const BuildingListTable = () => {
    const { t } = useTranslation()
    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)
    const [toDeleteId, setToDeleteId] = useState('')
    const [editModalOpen, setEditModalOpen] = useState(false)
    const [editingBuilding, setEditingBuilding] = useState<
        Buildings | undefined
    >()

    const handleCancel = () => {
        setDeleteConfirmationOpen(false)
    }

    const handleDelete = (building: Buildings) => {
        setDeleteConfirmationOpen(true)
        setToDeleteId(building.id || '')
    }

    const handleEdit = (building: Buildings) => {
        setEditingBuilding(building)
        setEditModalOpen(true)
    }

    const handleEditModalClose = () => {
        setEditModalOpen(false)
        setEditingBuilding(undefined)
    }

    const handleEditSuccess = () => {
        // Refresh the list after successful update
        mutate()
    }

    const handleConfirmDelete = () => {
        const newBuildingList = buildingList.filter((building) => {
            return !(toDeleteId === building.id)
        })
        setSelectAllBuilding([])
        mutate(
            {
                list: newBuildingList,
                total: buildingListTotal - selectedBuilding.length,
            },
            false,
        )
        setDeleteConfirmationOpen(false)
        setToDeleteId('')
    }

    const {
        buildingList,
        buildingListTotal,
        tableData,
        filterData,
        isLoading,
        setTableData,
        setSelectAllBuilding,
        setSelectedBuilding,
        selectedBuilding,
        mutate,
    } = useBuildingList()

    const allColumns: ColumnDef<Buildings>[] = useMemo(
        () => [
            {
                header: t('nav.buildings.name'),
                accessorKey: 'name',
                cell: (props) => {
                    const row = props.row.original
                    return <span className=" heading-text">{row.name}</span>
                },
            },
            {
                header: t('nav.shared.description'),
                accessorKey: 'description',
            },
            {
                header: t('nav.buildings.city'),
                accessorKey: 'city',
                cell: (props) => {
                    const row = props.row.original
                    return <span className=" heading-text">{row.city}</span>
                },
            },
            {
                header: t('nav.buildings.governorate'),
                accessorKey: 'governorate',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <span className=" heading-text">{row.governorate}</span>
                    )
                },
            },
            {
                header: t('nav.buildings.address'),
                accessorKey: 'address',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <span className=" heading-text">{row.address}</span>
                    )
                },
            },
            {
                header: t('nav.shared.status'),
                accessorKey: 'status',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <div className="flex items-center justify-center">
                            <Tag className={statusColor[row.status]}>
                                <span className="capitalize">{row.status}</span>
                            </Tag>
                        </div>
                    )
                },
            },
            {
                header: t('nav.shared.assignedBy'),
                accessorKey: 'assignedBy',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <span className=" heading-text">{row.assignedBy}</span>
                    )
                },
            },
            {
                header: t('nav.shared.createdAt'),
                accessorKey: 'createdAt',
            },
            {
                header: t('nav.shared.edit'),
                id: 'action',
                cell: (props) => (
                    <ActionColumn
                        onEdit={() => handleEdit(props.row.original)}
                        onDelete={() => handleDelete(props.row.original)}
                    />
                ),
            },
        ],
        [t],
    )

    const columns: ColumnDef<Buildings>[] = useMemo(() => {
        const visibleColumns = filterData.visibleColumns || []
        return allColumns.filter((column) => {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const key = (column as any).accessorKey || column.id
            return visibleColumns.includes(key) || key === 'action'
        })
    }, [allColumns, filterData.visibleColumns])

    const handleSetTableData = (data: TableQueries) => {
        setTableData(data)
        if (selectedBuilding.length > 0) {
            setSelectAllBuilding([])
        }
    }

    const handlePaginationChange = (page: number) => {
        const newTableData = cloneDeep(tableData)
        newTableData.pageIndex = page
        handleSetTableData(newTableData)
    }

    const handleSelectChange = (value: number) => {
        const newTableData = cloneDeep(tableData)
        newTableData.pageSize = Number(value)
        newTableData.pageIndex = 1
        handleSetTableData(newTableData)
    }

    const handleSort = (sort: OnSortParam) => {
        const newTableData = cloneDeep(tableData)
        newTableData.sort = sort
        handleSetTableData(newTableData)
    }

    const handleRowSelect = (checked: boolean, row: Buildings) => {
        setSelectedBuilding(checked, row)
    }

    const handleAllRowSelect = (checked: boolean, rows: Row<Buildings>[]) => {
        if (checked) {
            const originalRows = rows.map((row) => row.original)
            setSelectAllBuilding(originalRows)
        } else {
            setSelectAllBuilding([])
        }
    }

    return (
        <>
            <DataTable
                selectable
                columns={columns}
                data={buildingList}
                noData={!isLoading && buildingList.length === 0}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                loading={isLoading}
                pagingData={{
                    total: buildingListTotal,
                    pageIndex: tableData.pageIndex as number,
                    pageSize: tableData.pageSize as number,
                }}
                checkboxChecked={(row) =>
                    selectedBuilding.some(
                        (selected) => selected.id === row.id,
                    )
                }
                cellBorder={true}
                onPaginationChange={handlePaginationChange}
                onSelectChange={handleSelectChange}
                onSort={handleSort}
                onCheckBoxChange={handleRowSelect}
                onIndeterminateCheckBoxChange={handleAllRowSelect}
            />
            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title={t('nav.buildings.removeBuilding')}
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmDelete}
            >
                <p>
                    {' '}
                    {t('nav.buildings.confirmRemoveBuilding')}
                </p>
            </ConfirmDialog>

            <BuildingModal
                isOpen={editModalOpen}
                editData={editingBuilding}
                onSuccess={handleEditSuccess}
                onClose={handleEditModalClose}
            />
        </>
    )
}

export default BuildingListTable
