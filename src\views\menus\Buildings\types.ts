import type { Control, FieldErrors } from 'react-hook-form'

export type Buildings = {
    id?: string
    name: string
    city: string
    governorate: string
    address: string
    description: string
    status: string
    assignedBy?: string
    createdAt?: string
}

export type FormSectionBaseProps = {
    control: Control<Buildings>
    errors: FieldErrors<Buildings>
}

export type Filter = {
    buildingStatus: string
    visibleColumns: string[]
}

export type GetBuildingListResponse = {
    list: Buildings[]
    total: number
}
