
import ApiService from './ApiService'

export type Location = {
    code: string
    nameAr?: string
    nameEn?: string
}

export async function getCountries(): Promise<Location[]> {
    const response = await ApiService.get<Location[]>('/Location/countries')
    return response.countries
}

export async function getGovernorates(countryCode: string): Promise<Location[]> {
    const response = await ApiService.get<Location[]>(
        `/Location/countries/${countryCode}/governorates`
    )
    return response.governorates
}

export async function getCities( governorateCode: string): Promise<Location[]> {
    const response = await ApiService.get<Location[]>(
        `/Location/governorates/${governorateCode}/cities`
    )
    return response.cities
}

export async function getDistricts( cityCode: string): Promise<Location[]> {
    const response = await ApiService.get<Location[]>(
        `/Location/cities/${cityCode}/districts`
    )
    return response.districts
}


