import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import BuildingForm from './BuildingForm'
import sleep from '@/utils/sleep'
import type { Buildings } from '../types'
import useTranslation from '@/utils/hooks/useTranslation'

type BuildingModalProps = {
    isOpen: boolean
    onClose: () => void
    editData?: Buildings
    onSuccess?: () => void
}

const BuildingModal = ({
    isOpen,
    onClose,
    editData,
    onSuccess,
}: BuildingModalProps) => {
    const { t } = useTranslation()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const isEdit = !!editData

    const handleFormSubmit = async (values: Buildings) => {
        console.log('Submitted values', values)
        setIsSubmitting(true)
        await sleep(800)
        setIsSubmitting(false)

        toast.push(
            <Notification type="success">
                {isEdit ? 'Building updated!' : 'Building created!'}
            </Notification>,
            { placement: 'top-center' },
        )

        onSuccess?.()
        onClose()
    }

    const getDefaultValues = (): Buildings => {
        if (isEdit && editData) {
            return {
                id: editData.id,
                name: editData.name || '',
                city: editData.city || '',
                governorate: editData.governorate || '',
                address: editData.address || '',
                description: editData.description || '',
                status: editData.status || '',
                assignedBy: editData.assignedBy,
                createdAt: editData.createdAt,
            }
        }

        return {
            name: '',
            city: '',
            governorate: '',
            address: '',
            description: '',
            status: '',
        }
    }

    return (
        <>
            <Dialog
                isOpen={isOpen}
                shouldCloseOnOverlayClick={true}
                shouldCloseOnEsc={true}
                width={1000}
                className={'h-fit'}
                onClose={onClose}
                onRequestClose={onClose}
            >
                <div className="flex flex-col h-full">
                    <div className="flex items-center justify-center">
                        <h3 className="text-lg font-semibold">
                            {isEdit ? t('nav.shared.edit') + ' ' + t('nav.buildings.buildings') : t('nav.shared.add') + ' ' + t('nav.buildings.buildings')}
                        </h3>
                    </div>

                    <div className="flex-1 overflow-auto">
                        <BuildingForm
                            newBuilding={!isEdit}
                            defaultValues={getDefaultValues()}
                            onFormSubmit={handleFormSubmit}
                        >
                            <div className="flex items-center justify-between px-4 pt-4  bg-white dark:bg-gray-800">
                                <div>
                                    {isEdit && (
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                            {t('nav.shared.createdAt')} : {editData?.createdAt} 
                                        </div>
                                    )}
                                </div>
                                <Button
                                    variant="solid"
                                    type="submit"
                                    loading={isSubmitting}
                                >
                                    {isEdit ? t('nav.shared.update') : t('nav.shared.create')}
                                </Button>
                            </div>
                        </BuildingForm>
                    </div>
                </div>
            </Dialog>
        </>
    )
}

export default BuildingModal
