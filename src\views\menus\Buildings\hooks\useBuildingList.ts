import { buildingsData } from '@/mock/data/BuildingsData'
import { useBuildingListStore } from '../store/BuildingListStore'
import type { GetBuildingListResponse } from '../types'

const useBuildingList = () => {
    const {
        tableData,
        filterData,
        setTableData,
        setFilterData,
        selectedBuilding,
        setSelectedBuilding,
        setSelectAllBuilding,
    } = useBuildingListStore((state) => state)

    // Use mock data directly and map to correct type
    const mappedData = buildingsData.map(item => ({
        ...item,
        id: item.id.toString() // Convert numeric ID to string
    }))

    const data: GetBuildingListResponse = {
        list: mappedData,
        total: mappedData.length
    }

    const buildingList = data?.list || []

    const buildingListTotal = data?.total || 0

    // Mock mutate function for compatibility
    const mutate = () => {
        // No-op since we're using static mock data
    }

    return {
        error: null,
        isLoading: false,
        tableData,
        filterData,
        mutate,
        buildingList,
        buildingListTotal,
        setTableData,
        selectedBuilding,
        setSelectedBuilding,
        setSelectAllBuilding,
        setFilterData,
    }
}

export default useBuildingList
