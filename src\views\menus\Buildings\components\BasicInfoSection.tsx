import Card from '@/components/ui/Card'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import { FormItem } from '@/components/ui/Form'
import { Controller } from 'react-hook-form'
import type { FormSectionBaseProps } from '../types'
import { useTranslation } from 'react-i18next'

type BasicInfoSectionProps = FormSectionBaseProps

const statusOptions = [
    { value: 'معتمد', label: 'معتمد' },
    { value: 'غير معتمد', label: 'غير معتمد' },
]


const BasicInfoSection = ({ control, errors }: BasicInfoSectionProps) => {
    const { t } = useTranslation()

    return (
        <Card className="w-full p-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                <FormItem
                    className='mb-4'
                    label={t('nav.buildings.name')}
                    invalid={Boolean(errors.name)}
                    errorMessage={errors.name?.message}
                >
                    <Controller
                        name="name"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder={t('nav.buildings.enterName')}
                                {...field}
                            />
                        )}
                    />
                </FormItem>

                <FormItem
                    className='mb-4'
                    label={t('nav.shared.status')}
                    invalid={Boolean(errors.status)}
                    errorMessage={errors.status?.message}
                >
                    <Controller
                        name="status"
                        control={control}
                        render={({ field }) => (
                            <Select
                                options={statusOptions}
                                placeholder={t('nav.shared.selectStatus')}
                                value={statusOptions.filter(
                                    (option) => option.value === field.value,
                                )}
                                onChange={(option) =>
                                    field.onChange(option?.value || '')
                                }
                            />
                        )}
                    />
                </FormItem>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ">
            <FormItem
                    className='mb-4'
                    label={t('nav.buildings.city')}
                    invalid={Boolean(errors.city)}
                    errorMessage={errors.city?.message}
                >
                    <Controller
                        name="city"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder={t('nav.buildings.enterCity')}
                                value={field.value}
                                onChange={field.onChange}
                            />
                        )}
                    />
                </FormItem>

                <FormItem
                    className='mb-4'
                    label={t('nav.buildings.governorate')}
                    invalid={Boolean(errors.governorate)}
                    errorMessage={errors.governorate?.message}
                >
                    <Controller
                        name="governorate"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder={t('nav.buildings.enterGovernorate')}
                                value={field.value}
                                onChange={field.onChange}
                            />
                        )}
                    />
                </FormItem>

            </div>

           
                <FormItem
                    className='mb-4'
                    label={t('nav.buildings.address')}
                    invalid={Boolean(errors.address)}
                    errorMessage={errors.address?.message}
                >
                    <Controller
                        name="address"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder={t('nav.buildings.enterAddress')}
                                {...field}
                            />
                        )}
                    />
                </FormItem>

                <FormItem
                    className='mb-4'
                    label={t('nav.shared.description')}
                    invalid={Boolean(errors.description)}
                    errorMessage={errors.description?.message}
                >
                    <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder={t('nav.shared.enterDescription')}
                                {...field}
                            />
                        )}
                    />
                </FormItem>
        </Card>
    )
}

export default BasicInfoSection
