import { create } from 'zustand'
import { getCountries, getGovernorates, getCities, getDistricts } from '@/services/LocationService'
import type { Location } from '@/@types/locations'

type LocationState = {
    // Data
    countries: Location[]
    governorates: Location[]
    cities: Location[]
    districts: Location[]
    
    // Loading states
    isLoadingCountries: boolean
    isLoadingGovernorates: boolean
    isLoadingCities: boolean
    isLoadingDistricts: boolean
    
    // Error states
    countriesError: string | null
    governoratesError: string | null
    citiesError: string | null
    districtsError: string | null
    
    // Actions
    fetchCountries: () => Promise<void>
    fetchGovernorates: (countryCode: string) => Promise<void>
    fetchCities: (governorateCode: string) => Promise<void>
    fetchDistricts: (cityCode: string) => Promise<void>
    
    // Utility actions
    clearGovernorates: (countryCode?: string) => void
    clearCities: (governorateCode?: string) => void
    clearDistricts: (cityCode?: string) => void
    clearAll: () => void
    
    // Getters
    getCountryByCode: (code: string) => Location | undefined
    getGovernorateByCode: (code: string) => Location | undefined
    getCityByCode: (code: string) => Location | undefined
    getDistrictByCode: (code: string) => Location | undefined
}

const initialState = {
    countries: [],
    governorates: [],
    cities: [],
    districts: [],
    isLoadingCountries: false,
    isLoadingGovernorates: false,
    isLoadingCities: false,
    isLoadingDistricts: false,
    countriesError: null,
    governoratesError: null,
    citiesError: null,
    districtsError: null,
}

export const useLocationStore = create<LocationState>((set, get) => ({
    ...initialState,

    // Fetch countries
    fetchCountries: async () => {
        const { countries } = get()
        
        // Return cached data if already loaded
        if (countries.length > 0) {
            return
        }

        set({ isLoadingCountries: true, countriesError: null })
        
        try {
            const countriesData = await getCountries()
            set({ countries: countriesData, isLoadingCountries: false })
        } catch (error) {
            set({ 
                countriesError: error instanceof Error ? error.message : 'Failed to fetch countries',
                isLoadingCountries: false 
            })
        }
    },

    // Fetch governorates for a country
    fetchGovernorates: async (countryCode: string) => {
        const { governorates } = get()
        
        // Return cached data if already loaded
        if (Object.keys(governorates).length > 0) {
            return
        }

        set({ isLoadingGovernorates: true, governoratesError: null })
        
        try {
            const governoratesData = await getGovernorates(countryCode)
            set(({
                governorates: governoratesData,
                isLoadingGovernorates: false
            }))
        } catch (error) {
            set({ 
                governoratesError: error instanceof Error ? error.message : 'Failed to fetch governorates',
                isLoadingGovernorates: false 
            })
        }
    },

    // Fetch cities for a governorate
    fetchCities: async (governorateCode: string) => {
        const { cities } = get()
        
        // Return cached data if already loaded
        if (cities.length > 0) {
            return
        }

        set({ isLoadingCities: true, citiesError: null })
        
        try {
            const citiesData = await getCities(governorateCode)
            set(({
                cities: citiesData,
                isLoadingCities: false
            }))
        } catch (error) {
            set({ 
                citiesError: error instanceof Error ? error.message : 'Failed to fetch cities',
                isLoadingCities: false 
            })
        }
    },

    // Fetch districts for a city
    fetchDistricts: async (cityCode: string) => {
        const { districts } = get()
        
        // Return cached data if already loaded
        if (districts.length > 0) {
            return
        }

        set({ isLoadingDistricts: true, districtsError: null })
        
        try {
            const districtsData = await getDistricts(cityCode)
            set(({
                districts: districtsData,
                isLoadingDistricts: false
            }))
        } catch (error) {
            set({ 
                districtsError: error instanceof Error ? error.message : 'Failed to fetch districts',
                isLoadingDistricts: false 
            })
        }
    },

    // Clear governorates (optionally for specific country)
    clearGovernorates: (countryCode?: string) => {
        if (countryCode) {
            set({ governorates: [] })
        } else {
            set({ governorates: [] })
        }
    },

    // Clear cities (optionally for specific governorate)
    clearCities: (governorateCode?: string) => {
        if (governorateCode) {
            set({ cities: [] })
        } else {
            set({ cities: [] })
        }
    },

    // Clear districts (optionally for specific city)
    clearDistricts: (cityCode?: string) => {
        if (cityCode) {
            set({ districts: [] })
        } else {
            set({ districts: [] })
        }
    },

    // Clear all location data
    clearAll: () => {
        set(initialState)
    },

    // Get country by code
    getCountryByCode: (code: string) => {
        const { countries } = get()
        return countries.find(country => country.code === code)
    },

    // Get governorate by code
    getGovernorateByCode: (code: string) => {
        const { governorates } = get()
        const governorate = governorates.find(g => g.code === code)
        if (governorate) return governorate
        return undefined
    },

    // Get city by code
    getCityByCode: (code: string) => {
        const { cities } = get()
        const city = cities.find(c => c.code === code)
        if (city) return city
        return undefined
    },

    // Get district by code
    getDistrictByCode: (code: string) => {
        const { districts } = get()
            const district = districts.find(d => d.code === code)
        if (district) return district
        return undefined
    },
}))
