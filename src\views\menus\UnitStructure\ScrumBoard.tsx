import { useState } from 'react'
import { useOrganizationalStructure } from './hook/useOrganizationalStructure'
import { createOrganizeStructure, deleteOrganizeStructure } from '@/services/OrganizeStructureService'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import ScrumBoardHeader from './components/ScrumBoardHeader'
import BoardViews from './components/BoardViews'
import NodeDialog from './components/NodeDialog'
import type { Ticket } from './types'
import { flattenOrganizationalStructure } from './types'
import { DragDropContext, type DropResult } from '@hello-pangea/dnd'

const ScrumBoard = () => {
    const { organizationalStructure, refresh } = useOrganizationalStructure(true)
    const [newItem, setNewItem] = useState<{ name: string; description: string; parentCode: string }>({ name: '', description: '', parentCode: '' })
    const [isDialogOpen, setIsDialogOpen] = useState(false)
    const [selectedNode, setSelectedNode] = useState<Ticket | null>(null)

    const items: Ticket[] = organizationalStructure?.rootNodes
        ? flattenOrganizationalStructure(organizationalStructure.rootNodes)
        : []

    const onDragEnd = (result: DropResult) => {
        const { destination, draggableId } = result
        console.log(result)
        if (!destination) return console.log('no destination')

        // Handle new item drop
        if (draggableId === 'new-item') {
            const parentCode = destination.droppableId === 'root-list' ? null : destination.droppableId
            
            // Create the new organizational node
            const newNode = {
                name: newItem.name || 'New Item',
                description: newItem.description || '',
                parentCode: parentCode || ''
            }

            console.log(newNode)
            createOrganizeStructure(newNode)
                .then(async () => {
                    
                    setNewItem({ name: '', description: '', parentCode: '' })
                    await refresh()
                })
                .catch((error: Error) => {
                    console.log(error)
                })
        }
    }

    const handleNodeClick = (node: Ticket) => {
        setSelectedNode(node)
        setIsDialogOpen(true)
    }

    const handleDeleteNode = async (nodeId: string) => {
        try {
            await deleteOrganizeStructure(nodeId)
        } catch (error) {
            console.error('Error deleting node:', error)
            throw error
        }
    }

    return (
        <>
            <DragDropContext onDragEnd={onDragEnd}>
                <AdaptiveCard className="h-full" bodyClass="h-full flex flex-col">
                    <ScrumBoardHeader newItem={newItem} setNewItem={setNewItem} />
                    <div className="flex-1 overflow-auto">
                        <BoardViews items={items} onNodeClick={handleNodeClick} />
                    </div>
                </AdaptiveCard>
            </DragDropContext>
            
            {/* Node Dialog for editing/deleting */}
            <NodeDialog
                isOpen={isDialogOpen}
                node={selectedNode}
                onDelete={handleDeleteNode}
                onRefresh={refresh}
                onClose={() => {
                    setIsDialogOpen(false)
                    setSelectedNode(null)
                }}
            />
        </>
    )
}

export default ScrumBoard
