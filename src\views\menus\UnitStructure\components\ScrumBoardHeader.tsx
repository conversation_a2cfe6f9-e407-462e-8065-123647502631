import Input from '@/components/ui/Input'
import { TbPlus } from 'react-icons/tb'
import useTranslation from '@/utils/hooks/useTranslation'
import { Droppable, Draggable } from '@hello-pangea/dnd'

type ScrumBoardHeaderProps = {
    newItem: { name: string; description: string; parentCode: string }
    setNewItem: (item: { name: string; description: string; parentCode: string }) => void
}

const ScrumBoardHeader = ({ newItem, setNewItem }: ScrumBoardHeaderProps) => {
    const { t } = useTranslation()


    return (
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
            <div>
                <h3>{t('nav.unitStructure.structure')}</h3>
            </div>
            <div className="flex flex-col lg:flex-row justify-between lg:items-center gap-4">
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                    <div className="flex items-center gap-2">
                        <Input
                            placeholder={t('nav.unitStructure.itemName')}
                            value={newItem.name}
                            onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                            className="w-40"
                        />
                        <Input
                            placeholder={t('nav.unitStructure.description')}
                            value={newItem.description}
                            onChange={(e) => setNewItem({ ...newItem, description: e.target.value })}
                            className="w-48"
                        />
                        <Droppable droppableId="new-item-container" type="ITEMS">
                            {(provided, snapshot) => (
                                <div
                                    ref={provided.innerRef}
                                    {...provided.droppableProps}
                                    className={`p-2 rounded-lg border-2  transition-colors ${
                                        snapshot.isDraggingOver 
                                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                                            : 'border-gray-300 dark:border-gray-600'
                                    }`}
                                >
                                    <Draggable draggableId="new-item" index={0} isDragDisabled={newItem.name === ''}>
                                        {(provided, dragSnapshot) => (
                                            <div
                                                ref={provided.innerRef}
                                                {...provided.draggableProps}
                                                {...provided.dragHandleProps}
                                                className={`transition-all duration-200 ${
                                                    dragSnapshot.isDragging ? 'opacity-75 scale-105' : ''
                                                }`}
                                            >
                                                <div className="bg-blue-100 dark:bg-blue-800 border border-blue-300 dark:border-blue-600 rounded-lg p-2 shadow-sm hover:shadow-md transition-all cursor-grab active:cursor-grabbing">
                                                    <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                                                        <TbPlus size={16} />
                                                        <span className="text-sm font-medium">
                                                            {newItem.name || t('nav.unitStructure.dragToAddNewItem')}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </Draggable>
                                    {provided.placeholder}
                                </div>
                            )}
                        </Droppable>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ScrumBoardHeader
