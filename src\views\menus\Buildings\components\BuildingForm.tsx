import { useEffect } from 'react'
import { Form } from '@/components/ui/Form'
import Container from '@/components/shared/Container'
import BottomStickyBar from '@/components/template/BottomStickyBar'
import BasicInfoSection from './BasicInfoSection'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import isEmpty from 'lodash/isEmpty'
import type { Buildings } from '../types'
import type { ZodType } from 'zod'
import type { CommonProps } from '@/@types/common'

type BuildingFormProps = {
    onFormSubmit: (values: Buildings) => void
    defaultValues?: Buildings
    newBuilding?: boolean
} & CommonProps

const validationSchema: ZodType<Buildings> = z.object({
    name: z.string().min(1, { message: 'Name is required!' }),
    city: z.string().min(1, { message: 'City is required!' }),
    governorate: z.string().min(1, { message: 'Governorate is required!' }),
    address: z.string().min(1, { message: 'Address is required!' }),
    description: z.string().min(1, { message: 'Description is required!' }),
    status: z.string().min(1, { message: 'Status is required!' }),
    assignedBy: z.string().min(1, { message: 'Assigned By is required!' }),
    createdAt: z.string().optional(),
})

const BuildingForm = (props: BuildingFormProps) => {
    const { onFormSubmit, defaultValues = {}, children } = props

    const {
        handleSubmit,
        reset,
        formState: { errors },
        control,
    } = useForm<Buildings>({
        defaultValues: {
            ...defaultValues,
        },
        resolver: zodResolver(validationSchema),
    })

    useEffect(() => {
        if (!isEmpty(defaultValues)) {
            reset(defaultValues)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [JSON.stringify(defaultValues)])

    const onSubmit = (values: Buildings) => {
        onFormSubmit?.(values)
    }

    return (
        <Form
            className="flex w-full h-full "
            containerClassName="flex flex-col w-full justify-between gap-4"
            onSubmit={handleSubmit(onSubmit)}
        >
            <Container>
                <div className="flex flex-col sm:flex-row justify-between gap-4 mt-4">
                    <BasicInfoSection control={control} errors={errors} />
                </div>
            </Container>
            <BottomStickyBar>{children}</BottomStickyBar>
        </Form>
    )
}

export default BuildingForm
